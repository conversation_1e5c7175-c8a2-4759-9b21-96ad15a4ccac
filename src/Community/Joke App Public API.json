{"name": "Joke App using Public API", "description": "It take a public api output to get a random joke and Rephrase using LLM", "icon": "User", "color": "#10B981", "nodes": [{"id": "api_call_1742912346201", "type": "apiCallNode", "position": {"x": 260.0922105761523, "y": 184.82289604862666}, "data": {"label": "API Call", "labelStyle": {"color": "#000"}, "tool": {"id": "api_call", "name": "API Call", "description": "Make external API requests", "color": "bg-red-500", "bgColor": "bg-red-100", "lightColor": "#EF4444", "darkColor": "#F87171", "category": "function", "inputs": ["text"], "outputs": ["text"], "iconName": "Settings", "icon": {}}, "inputs": ["text"], "outputs": ["text"], "config": {"endpoint": "https://v2.jokeapi.dev/joke/Any", "responseStatus": 200, "responseData": "{\"error\":false,\"category\":\"Programming\",\"type\":\"twopart\",\"setup\":\"Why was the JavaScript developer sad?\",\"delivery\":\"Because they didn't Node how to Express themself!\",\"flags\":{\"nsfw\":false,\"religious\":false,\"political\":false,\"racist\":false,\"sexist\":false,\"explicit\":false},\"safe\":true,\"id\":291,\"lang\":\"en\"}", "headers": [], "queryParams": []}}, "width": 320, "height": 250, "selected": false, "positionAbsolute": {"x": 260.0922105761523, "y": 184.82289604862666}, "dragging": false, "style": {"boxShadow": "none"}}, {"id": "base_llm_1742912579491", "type": "baseLlmNode", "position": {"x": -66.60882101026561, "y": 511.0876641241143}, "data": {"label": "LLM Prompt", "labelStyle": {"color": "#000"}, "tool": {"id": "base_llm", "name": "LLM Prompt", "description": "Process text with an LLM", "color": "bg-purple-500", "bgColor": "bg-purple-100", "lightColor": "#8B5CF6", "darkColor": "#A78BFA", "category": "process", "inputs": ["text"], "outputs": ["text"], "iconName": "Activity", "icon": {}}, "inputs": ["text"], "outputs": ["text"], "config": {"apiType": "ollama", "ollamaUrl": "http://localhost:11434", "apiKey": "", "openaiUrl": "http://***********:1234/v1", "model": "gemma3:4b", "prompt": "repharse the joke and tell me in a best way,\n\nJust the joke nothing else"}}, "width": 288, "height": 335, "selected": false, "positionAbsolute": {"x": -66.60882101026561, "y": 511.0876641241143}, "dragging": false, "style": {"boxShadow": "none"}}, {"id": "text_output_1742912586283", "type": "textOutputNode", "position": {"x": 276.76356489007355, "y": 511.0876641241143}, "data": {"label": "Text Output", "labelStyle": {"color": "#000"}, "tool": {"id": "text_output", "name": "Text Output", "description": "Display text to users", "color": "bg-green-500", "bgColor": "bg-green-100", "lightColor": "#10B981", "darkColor": "#34D399", "category": "output", "inputs": ["text"], "outputs": [], "iconName": "<PERSON><PERSON><PERSON><PERSON>", "icon": {}}, "inputs": ["text"], "outputs": [], "config": {"outputText": ""}}, "width": 280, "height": 113, "selected": false, "positionAbsolute": {"x": 276.76356489007355, "y": 511.0876641241143}, "dragging": false, "style": {"boxShadow": "none"}}, {"id": "static_text_1742912642527", "type": "staticTextNode", "position": {"x": -55.39666147066271, "y": 167.71527822377521}, "data": {"label": "Static Text", "labelStyle": {"color": "#000"}, "tool": {"id": "static_text", "name": "Static Text", "description": "Fixed text content that does not change", "color": "bg-red-500", "bgColor": "bg-red-100", "lightColor": "#F87171", "darkColor": "#DC2626", "category": "input", "inputs": [], "outputs": ["text"], "iconName": "TextQuote", "icon": {}}, "inputs": [], "outputs": ["text"], "config": {"staticText": "Repharse a joke and make if funny"}}, "width": 256, "height": 240, "selected": false, "positionAbsolute": {"x": -55.39666147066271, "y": 167.71527822377521}, "dragging": false}], "edges": [{"source": "api_call_1742912346201", "sourceHandle": "text-out", "target": "base_llm_1742912579491", "targetHandle": "text-in", "animated": true, "type": "smoothstep", "style": {"stroke": "#F472B6", "strokeWidth": 2}, "id": "reactflow__edge-api_call_1742912346201text-out-base_llm_1742912579491text-in"}, {"source": "base_llm_1742912579491", "sourceHandle": "text-out", "target": "text_output_1742912586283", "targetHandle": "text-in", "animated": true, "type": "smoothstep", "style": {"stroke": "#F472B6", "strokeWidth": 2}, "id": "reactflow__edge-base_llm_1742912579491text-out-text_output_1742912586283text-in"}, {"source": "static_text_1742912642527", "sourceHandle": null, "target": "api_call_1742912346201", "targetHandle": "text-in", "animated": true, "type": "smoothstep", "style": {"stroke": "#F472B6", "strokeWidth": 2}, "id": "reactflow__edge-static_text_1742912642527-api_call_1742912346201text-in"}]}
{"name": "N8N Workflow with App builder", "description": "LLM having access to all my emails and calendar ", "icon": "Brain", "color": "#EF4444", "nodes": [{"id": "api_call_1744647080437", "type": "apiCallNode", "position": {"x": 566.481383232599, "y": -274.05293673358204}, "data": {"label": "API Call", "labelStyle": {"color": "#fff"}, "tool": {"id": "api_call", "name": "API Call", "description": "Make external API requests", "color": "bg-red-500", "bgColor": "bg-red-100", "lightColor": "#EF4444", "darkColor": "#F87171", "category": "function", "inputs": ["text"], "outputs": ["text"], "iconName": "Globe", "icon": {}}, "inputs": ["text"], "outputs": ["text"], "config": {"endpoint": "http://localhost:5678/webhook/7aba4f2e-2b06-4f0f-83bf-b26b2676caa9?query={{input}}", "responseStatus": 200, "responseData": "[{\"output\":\"Hello! To better assist you, would you like to check your calendar for upcoming events today or in the next few days? Or perhaps you need help adding a new event?\"}]"}}, "width": 320, "height": 240, "selected": false, "dragging": false, "style": {"boxShadow": "none", "zIndex": 1000}, "positionAbsolute": {"x": 566.481383232599, "y": -274.05293673358204}}, {"id": "text_input_1744647102804", "type": "textInputNode", "position": {"x": 185.07862390732356, "y": -3.594506623925497}, "data": {"label": "Text Input", "labelStyle": {"color": "#fff"}, "tool": {"id": "text_input", "name": "Text Input", "description": "Accept text input from users", "color": "bg-blue-500", "bgColor": "bg-blue-100", "lightColor": "#3B82F6", "darkColor": "#60A5FA", "category": "input", "inputs": [], "outputs": ["text"], "iconName": "<PERSON><PERSON><PERSON><PERSON>", "icon": {}}, "inputs": [], "outputs": ["text"], "config": {"text": "list all the email from m<PERSON><PERSON>"}}, "width": 280, "height": 237, "selected": false, "positionAbsolute": {"x": 185.07862390732356, "y": -3.594506623925497}, "dragging": false, "style": {"boxShadow": "none"}}, {"id": "text_output_1744650074162", "type": "textOutputNode", "position": {"x": 521.6894218015188, "y": 510.70910585984905}, "data": {"label": "Text Output", "labelStyle": {"color": "#fff"}, "tool": {"id": "text_output", "name": "Text Output", "description": "Display text to users", "color": "bg-green-500", "bgColor": "bg-green-100", "lightColor": "#10B981", "darkColor": "#34D399", "category": "output", "inputs": ["text"], "outputs": [], "iconName": "<PERSON><PERSON><PERSON><PERSON>", "icon": {}}, "inputs": ["text"], "outputs": [], "config": {"outputText": ""}}, "width": 280, "height": 113, "selected": false, "positionAbsolute": {"x": 521.6894218015188, "y": 510.70910585984905}, "dragging": false, "style": {"boxShadow": "none", "zIndex": 1000}}, {"id": "text_input_preview_1744699069813", "type": "textInputPreviewNode", "position": {"x": 890.2219053309004, "y": 1.3071525993080257}, "data": {"label": "JSON Key Extractor", "labelStyle": {"color": "#000"}, "tool": {"id": "text_input_preview", "name": "JSON Key Extractor", "description": "Extract values from JSON using a specified key", "color": "bg-blue-500", "bgColor": "bg-blue-100", "lightColor": "#3B82F6", "darkColor": "#60A5FA", "category": "string_manipulation", "inputs": ["text"], "outputs": ["text"], "iconName": "<PERSON><PERSON><PERSON><PERSON>", "icon": {}}, "inputs": ["text"], "outputs": ["text"], "config": {"jsonKey": "output[0].output", "text": "{\"input\":\"hi\",\"output\":[{\"output\":\"Hello! Would you like to check your calendar for any upcoming events or view your schedule for the next few days? If so, please provide a date range, and I can help you with that.\"}]}"}, "extractedValue": "Hello! Would you like to check your calendar for any upcoming events or view your schedule for the next few days? If so, please provide a date range, and I can help you with that."}, "width": 280, "height": 422, "selected": true, "positionAbsolute": {"x": 890.2219053309004, "y": 1.3071525993080257}, "dragging": false, "style": {"boxShadow": "0 0 0 2px #F472B6, 0 0 10px 2px rgba(244, 114, 182, 0.5)", "zIndex": 1000}}], "edges": [{"source": "text_input_1744647102804", "sourceHandle": "text-out", "target": "api_call_1744647080437", "targetHandle": "text-in", "animated": true, "type": "smoothstep", "style": {"stroke": "#EC4899", "strokeWidth": 2}, "id": "reactflow__edge-text_input_1744647102804text-out-api_call_1744647080437text-in"}, {"source": "api_call_1744647080437", "sourceHandle": "text-out", "target": "text_input_preview_1744699069813", "targetHandle": "text-in", "animated": true, "type": "smoothstep", "style": {"stroke": "#F472B6", "strokeWidth": 2}, "id": "reactflow__edge-api_call_1744647080437text-out-text_input_preview_1744699069813text-in"}, {"source": "text_input_preview_1744699069813", "sourceHandle": "text-out", "target": "text_output_1744650074162", "targetHandle": "text-in", "animated": true, "type": "smoothstep", "style": {"stroke": "#F472B6", "strokeWidth": 2}, "id": "reactflow__edge-text_input_preview_1744699069813text-out-text_output_1744650074162text-in"}]}
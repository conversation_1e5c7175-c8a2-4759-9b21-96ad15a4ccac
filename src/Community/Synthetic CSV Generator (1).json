{"name": "Sythetic CSV Generator", "description": "It Create CSV for you based on what you ask,\nRequires better llm atleast 8B and Up", "icon": "Bot", "color": "#EF4444", "nodes": [{"id": "structured_llm_1742896491188", "type": "structuredLlmNode", "position": {"x": 391.21147838550996, "y": -84.88365081894618}, "data": {"label": "Structured LLM", "labelStyle": {"color": "#000"}, "tool": {"id": "structured_llm", "name": "Structured LLM", "description": "Parses JSON and generates structured output based on a question or text input", "color": "bg-purple-500", "bgColor": "bg-purple-100", "lightColor": "#8B5CF6", "darkColor": "#A78BFA", "category": "process", "inputs": ["text"], "outputs": ["text"], "iconName": "Activity", "icon": {}}, "inputs": ["text"], "outputs": ["text"], "config": {"apiType": "openai", "ollamaUrl": "http://localhost:11434", "openaiUrl": "https://api.openai.com/v1", "model": "gpt-4o-mini", "structuredFormat": "{\n  \"cars\": [\n    {\n      \"make\": \"Toyota\",\n      \"model\": \"Corolla\",\n      \"year\": \"2022\",\n      \"price\": \"20000\"\n    },\n    {\n      \"make\": \"Honda\",\n      \"model\": \"Civic\",\n      \"year\": \"2021\",\n      \"price\": \"22000\"\n    },\n    {\n      \"make\": \"Ford\",\n      \"model\": \"Mustang\",\n      \"year\": \"2023\",\n      \"price\": \"30000\"\n    },\n    {\n      \"make\": \"Chevrolet\",\n      \"model\": \"Malibu\",\n      \"year\": \"2020\",\n      \"price\": \"18000\"\n    },\n    {\n      \"make\": \"Nissan\",\n      \"model\": \"Altima\",\n      \"year\": \"2019\",\n      \"price\": \"17000\"\n    }\n  ]\n}", "prompt": "You always provide json format in what user asks for its your only job, your job is to create example json for the size they ask\n\nAlways follow the user's instruction cand give what they ask", "apiKey": ""}}, "width": 288, "height": 449, "selected": true, "dragging": false, "style": {"boxShadow": "none"}, "positionAbsolute": {"x": 391.21147838550996, "y": -84.88365081894618}}, {"id": "text_input_1742896499377", "type": "textInputNode", "position": {"x": 51.78633454729891, "y": 42.181987403103335}, "data": {"label": "Text Input", "labelStyle": {"color": "#000"}, "tool": {"id": "text_input", "name": "Text Input", "description": "Accept text input from users", "color": "bg-blue-500", "bgColor": "bg-blue-100", "lightColor": "#3B82F6", "darkColor": "#60A5FA", "category": "input", "inputs": [], "outputs": ["text"], "iconName": "<PERSON><PERSON><PERSON><PERSON>", "icon": {}}, "inputs": [], "outputs": ["text"], "config": {"text": "i need a 5 worlds fastest car details dont chnage the structure "}}, "width": 280, "height": 237, "selected": false, "positionAbsolute": {"x": 51.78633454729891, "y": 42.181987403103335}, "dragging": false, "style": {"boxShadow": "none"}}, {"id": "json_to_csv_1742906038903", "type": "jsonToCsvNode", "position": {"x": 731.9759830106195, "y": -113.7969198311784}, "data": {"label": "JSON to CSV", "labelStyle": {"color": "#000"}, "tool": {"id": "json_to_csv", "name": "JSON to CSV", "description": "Convert JSON data to CSV format", "color": "bg-yellow-500", "bgColor": "bg-yellow-100", "lightColor": "#F59E0B", "darkColor": "#FBBF24", "category": "function", "inputs": ["text"], "outputs": ["text"], "iconName": "<PERSON><PERSON><PERSON>", "icon": {}}, "inputs": ["text"], "outputs": ["text"], "config": {"csvOutput": "name,top_speed,horsepower,engine\nSSC Tuatara,282.9 mph,\"1,750 hp\",5.9L twin-turbo V8\nBugatti Chiron Super Sport 300+,304.77 mph,\"1,577 hp\",8.0L quad-turbo W16\nKoenigsegg Jesko Absolut,331 mph,\"1,600 hp\",5.0L twin-turbo V8\nHennessey Venom F5,301 mph,\"1,817 hp\",6.6L twin-turbo V8\nRimac Nevera,258 mph,\"1,914 hp\",4 electric motors"}}, "width": 288, "height": 530, "selected": false, "positionAbsolute": {"x": 731.9759830106195, "y": -113.7969198311784}, "dragging": false, "style": {"boxShadow": "none"}}, {"id": "text_output_1742906162460", "type": "textOutputNode", "position": {"x": 338.8210133013837, "y": 470.6069993834161}, "data": {"label": "Text Output", "labelStyle": {"color": "#000"}, "tool": {"id": "text_output", "name": "Text Output", "description": "Display text to users", "color": "bg-green-500", "bgColor": "bg-green-100", "lightColor": "#10B981", "darkColor": "#34D399", "category": "output", "inputs": ["text"], "outputs": [], "iconName": "<PERSON><PERSON><PERSON><PERSON>", "icon": {}}, "inputs": ["text"], "outputs": [], "config": {"outputText": ""}}, "width": 280, "height": 113, "selected": false, "dragging": false, "style": {"boxShadow": "0 0 0 2px #F472B6, 0 0 10px 2px rgba(244, 114, 182, 0.5)", "zIndex": 1000}, "positionAbsolute": {"x": 338.8210133013837, "y": 470.6069993834161}}], "edges": [{"source": "text_input_1742896499377", "sourceHandle": "text-out", "target": "structured_llm_1742896491188", "targetHandle": "prompt-input", "animated": true, "type": "smoothstep", "style": {"stroke": "#F472B6", "strokeWidth": 2}, "id": "reactflow__edge-text_input_1742896499377text-out-structured_llm_1742896491188prompt-input"}, {"source": "structured_llm_1742896491188", "sourceHandle": "text", "target": "json_to_csv_1742906038903", "targetHandle": "text", "animated": true, "type": "smoothstep", "style": {"stroke": "#F472B6", "strokeWidth": 2}, "id": "reactflow__edge-structured_llm_1742896491188text-json_to_csv_1742906038903text"}, {"source": "json_to_csv_1742906038903", "sourceHandle": "text", "target": "text_output_1742906162460", "targetHandle": "text-in", "animated": true, "type": "smoothstep", "style": {"stroke": "#F472B6", "strokeWidth": 2}, "id": "reactflow__edge-json_to_csv_1742906038903text-text_output_1742906162460text-in"}]}
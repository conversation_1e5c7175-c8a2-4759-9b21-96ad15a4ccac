{"sections": [{"id": "getting-started", "title": "Getting Started", "content": "## Setting Up Clara\n\nClara requires two main components to function:\n\n### 1. <PERSON><PERSON>ma (Required for AI Chat)\n\n1. Visit [<PERSON>lla<PERSON>'s official website](https://ollama.com)\n2. Download and install Ollama for your operating system\n3. After installation, <PERSON>llama will run in the background\n4. No additional configuration is needed\n\n### 2. ComfyUI (Required for Image Generation)\n\n1. Visit [ComfyUI's website](https://www.comfy.org/download)\n2. Follow the installation instructions for your platform\n3. Start ComfyUI before using <PERSON>'s image generation features\n\n### First Steps\n\n1. Complete the onboarding process\n2. Configure your API endpoints in Settings\n3. Download your preferred AI models using the Model Manager\n\n### ComfyUI Node Issues\nIf you encounter errors like 'LoRA Stacker does not exist' or have issues with image generation, install these required repositories:\n\n1. [A<PERSON>ly's ComfyUI Tooling Nodes](https://github.com/Acly/comfyui-tooling-nodes)\n2. [Jags111's Efficiency Nodes](https://github.com/jags111/efficiency-nodes-comfyui)\n\nFollow the installation instructions in each repository.\n\n### Multiple VAEs and LoRAs\n- Currently, stacking multiple LoRAs is disabled\n- Future updates will enable this feature\n\n### Image Support & Models\n1. Go to Assistant Settings\n2. Find your model in the list\n3. Enable 'Image Support' toggle\n4. Models like Gemma3 and Minicpm2.6 will now work with images\n\n### Image Generation & Prompting\n- Use App Builder to create custom image generation flows\n- Import pre-made workflows from [ClaraVerse](https://github.com/badboysm890/ClaraVerse)\n- Example workflow: [Image and Text to Image.json](https://github.com/badboysm890/ClaraVerse/blob/e98211128729f9245d1f38f95aaee72639911490/Image%20and%20Text%20to%20Image.json)\n\n### Upcoming Features\n- Enhanced prompt generation\n- Automatic parameter configuration\n- Community tab for sharing workflows\n- Workflow marketplace integration"}, {"id": "features", "title": "Features Overview", "content": "## Clara's Main Features\n\n### 1. AI Chat Assistant\n- Multiple local model support (LLaMA, Mistral, etc.)\n- Image understanding capabilities\n- Streaming and context-aware conversations\n- System prompt and persona customization\n- Local-first inference via Ollama\n\n### 2. App Creation & Automation\n- Visual flow builder to create AI-powered apps\n- Export workflows as standalone local apps\n- Drag-and-drop UI with multiple input/output nodes\n- App templates and ClaraVerse community library\n\n### 3. Image Generation\n- Text-to-image generation using Stable Diffusion\n- Support for ControlNet, LoRA, and VAE\n- Image editing and remixing capabilities\n- Model management with prompt presets\n\n### 4. Workflow Automation\n- Create chained tasks using different models\n- Integrate chat + image + custom logic\n- Automation for routine creative or assistant tasks\n\n### 5. Local Model Management (Coming Soon)\n- Download, switch, and manage local models in-app\n- Automatically detect available Ollama models\n- Support for OpenAI-compatible backends in future\n\n### 6. Privacy & Offline-First\n- Runs fully on macOS, Windows, and Linux\n- No data leaves your device\n- Zero tracking or telemetry"}, {"id": "tips", "title": "Tips & Tricks", "content": "## Maximizing <PERSON>'s Potential\n\n### Chat Assistant Tips\n- Use clear, specific prompts\n- Utilize system messages to set tone or behavior\n- Take advantage of image understanding\n- Save useful conversations for reuse\n\n### Image Generation Tips\n- Be descriptive with prompts\n- Use negative prompts to refine outputs\n- Mix models and try different styles\n- Save successful settings as templates\n\n### App Creation Tips\n- Start with a pre-made template from ClaraVerse\n- Test workflows one step at a time\n- Use debug view for identifying issues\n- Share your creations with others to improve them"}, {"id": "common-issues", "title": "Common Issues & Solutions", "content": "## Common Issues & Solutions\n\n### ComfyUI Node Issues\nIf you encounter errors like 'LoRA Stacker does not exist' or have issues with image generation, install these required repositories:\n\n1. [Acly's ComfyUI Tooling Nodes](https://github.com/Acly/comfyui-tooling-nodes)\n2. [Jags111's Efficiency Nodes](https://github.com/jags111/efficiency-nodes-comfyui)\n\n### Multiple VAEs and LoRAs\n- Currently, stacking multiple LoRAs is disabled\n- This feature will be added in future updates\n\n### Image Support & Models\n- Go to Assistant Settings\n- Enable 'Image Support' for supported models\n- Models like Gemma3, Minicpm2.6 will then support image input\n\n### Image Generation Issues\n- Make sure ComfyUI is running before launching image tools\n- Use the latest ClaraVerse workflows to avoid missing node issues\n\n### General Troubleshooting\n- Restart Clara after changing settings\n- Ensure backend tools (Ollama/ComfyUI) are active\n- Check logs via debug mode if something breaks\n\n### Support\n- Open an issue on GitHub\n- Join the community Discord (link in README)\n- Check [ClaraVerse](https://github.com/badboysm890/ClaraVerse) for updated workflows"}]}
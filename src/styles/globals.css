/* Resizable Widget Styles */
.resizable-widget {
  position: relative;
  transition: all 0.2s ease;
  width: 100%;
  height: 100%;
}

.resizable-widget:hover .resize-handle {
  opacity: 1;
}

.resize-handle {
  position: absolute;
  opacity: 0;
  transition: opacity 0.2s ease;
  background-color: rgba(219, 39, 119, 0.3); /* sakura color */
  border: 2px solid rgba(219, 39, 119, 0.5);
  z-index: 50;
}

.resize-handle:hover {
  background-color: rgba(219, 39, 119, 0.5);
  transform: scale(1.1);
  transition: all 0.2s ease;
}

.widget-container {
  height: 100%;
  width: 100%;
  overflow: hidden;
  border-radius: 1rem;
  position: relative;
}

/* Dark mode adjustments */
.dark .resize-handle {
  background-color: rgba(244, 114, 182, 0.3); /* lighter sakura color for dark mode */
  border: 2px solid rgba(244, 114, 182, 0.5);
}

.dark .resize-handle:hover {
  background-color: rgba(244, 114, 182, 0.5);
}

/* Specific handle styles */
.resize-handle-n {
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 8px;
  cursor: ns-resize;
  border-radius: 4px;
}

.resize-handle-s {
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 8px;
  cursor: ns-resize;
  border-radius: 4px;
}

.resize-handle-e {
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  width: 8px;
  height: 40px;
  cursor: ew-resize;
  border-radius: 4px;
}

.resize-handle-w {
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  width: 8px;
  height: 40px;
  cursor: ew-resize;
  border-radius: 4px;
}

.resize-handle-ne {
  top: 0;
  right: 0;
  width: 16px;
  height: 16px;
  cursor: ne-resize;
  border-radius: 50%;
}

.resize-handle-nw {
  top: 0;
  left: 0;
  width: 16px;
  height: 16px;
  cursor: nw-resize;
  border-radius: 50%;
}

.resize-handle-se {
  bottom: 0;
  right: 0;
  width: 16px;
  height: 16px;
  cursor: se-resize;
  border-radius: 50%;
}

.resize-handle-sw {
  bottom: 0;
  left: 0;
  width: 16px;
  height: 16px;
  cursor: sw-resize;
  border-radius: 50%;
} 
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Welcome to ClaraVerse</title>

</head>
<body>
  <div class="dashboard">
    <!-- Header Section -->
    <header>
      <div class="brand">
        <div class="logo" id="app-logo">C</div>
        <div class="brand-text">
          <h1>ClaraVerse</h1>
          <p class="subtitle">AI-powered creativity and automation platform</p>
        </div>
      </div>
    </header>

    <!-- Sidebar Section -->
    <aside>
      <div class="info-card">
        <h3>🎯 Getting Started</h3>
        <p>ClaraVerse requires a container engine to run AI models and services securely in isolated environments.</p>
      </div>

      <div class="info-card">
        <h3>🔒 Why Containers?</h3>
        <p>Containers provide security, consistency, and easy management of complex AI dependencies.</p>
      </div>

      <div class="installation-guide hidden" id="installation-guide">
        <h3>🚀 Quick Setup</h3>
        <p><strong>Recommended:</strong> Install Podman (more secure, no root required)</p>
        <p id="install-command">• macOS: <code>brew install podman</code></p>
        <p>• Linux: <code>sudo apt install podman</code> or <code>sudo dnf install podman</code></p>
        <p>• Windows: Download from <a href="#" onclick="openExternal('https://podman-desktop.io/')">podman-desktop.io</a></p>
        <br>
        <p><strong>Alternative:</strong> Install Docker Desktop</p>
        <p>• Download from <a href="#" onclick="openExternal('https://www.docker.com/products/docker-desktop')">docker.com</a></p>
      </div>
    </aside>

    <!-- Main Content Section -->
    <main>
      <section class="status-section">
        <h2>🔍 System Requirements</h2>

        <div class="status-grid">
          <article class="status-item" id="container-status">
            <div class="status-icon checking" id="container-icon">?</div>
            <div class="status-content">
              <div class="status-title">Container Engine</div>
              <div class="status-detail" id="container-detail">Checking for Podman or Docker...</div>
            </div>
          </article>

          <article class="status-item" id="network-status">
            <div class="status-icon checking" id="network-icon">?</div>
            <div class="status-content">
              <div class="status-title">Network Access</div>
              <div class="status-detail" id="network-detail">Checking internet connectivity...</div>
            </div>
          </article>

          <article class="status-item" id="storage-status">
            <div class="status-icon checking" id="storage-icon">?</div>
            <div class="status-content">
              <div class="status-title">Storage Space</div>
              <div class="status-detail" id="storage-detail">Checking available disk space...</div>
            </div>
          </article>
        </div>
      </section>
    </main>

    <!-- Actions Panel -->
    <div class="actions-panel">
      <h3>⚡ Actions</h3>

      <button class="btn hidden" id="continue-btn" onclick="continueSetup()">
        <span>🚀</span>
        Continue Setup
      </button>

      <button class="btn secondary" id="retry-btn" onclick="retryCheck()" disabled>
        <span class="spinner hidden" id="retry-spinner"></span>
        <span id="retry-text">🔄 Retry Check</span>
      </button>

      <button class="btn secondary" onclick="openGuide()">
        <span>📖</span>
        Setup Guide
      </button>

      <button class="btn secondary" onclick="skipSetup()">
        <span>⏭️</span>
        Skip for Now
      </button>
    </div>

    <!-- Footer Section -->
    <footer>
      <div class="help-links">
        <span>Need help?</span>
        <a href="#" onclick="openExternal('https://github.com/badboysm890/ClaraVerse/blob/main/PODMAN_SETUP.md')">Setup Guide</a>
        <a href="#" onclick="openExternal('https://github.com/badboysm890/ClaraVerse/issues')">Report Issue</a>
      </div>
      <div>
        <small>ClaraVerse v1.0.0</small>
      </div>
    </footer>
  </div>

  <script>
    const { ipcRenderer } = require('electron');

    let checkInProgress = false;

    async function checkPrerequisites() {
      if (checkInProgress) return;
      checkInProgress = true;

      const retryBtn = document.getElementById('retry-btn');
      const retrySpinner = document.getElementById('retry-spinner');
      retryBtn.disabled = true;
      retrySpinner.classList.remove('hidden');

      try {
        // Check container engine
        const containerResult = await ipcRenderer.invoke('check-container-engine');
        updateContainerStatus(containerResult);

        // Check network
        const networkResult = await ipcRenderer.invoke('check-network');
        updateNetworkStatus(networkResult);

        // Check storage
        const storageResult = await ipcRenderer.invoke('check-storage');
        updateStorageStatus(storageResult);

        // Update UI based on results
        updateActions(containerResult, networkResult, storageResult);

      } catch (error) {
        console.error('Error checking prerequisites:', error);
      } finally {
        checkInProgress = false;
        retryBtn.disabled = false;
        retrySpinner.classList.add('hidden');
      }
    }

    function updateContainerStatus(result) {
      const icon = document.getElementById('container-icon');
      const detail = document.getElementById('container-detail');
      const statusItem = document.getElementById('container-status');

      if (result.available) {
        if (result.status === 'running') {
          icon.className = 'status-icon success';
          icon.textContent = '✓';
          detail.textContent = `${result.engine} is available and running`;
          statusItem.style.borderColor = '#27ae60';
        } else {
          icon.className = 'status-icon warning';
          icon.textContent = '!';
          detail.textContent = `${result.engine} is installed but ${result.details || 'not running'}`;
          statusItem.style.borderColor = '#f39c12';
        }
      } else {
        icon.className = 'status-icon error';
        icon.textContent = '✗';
        detail.textContent = result.details || 'No container engine found. Please install Podman or Docker.';
        statusItem.style.borderColor = '#e74c3c';
        document.getElementById('installation-guide').classList.remove('hidden');
      }
    }

    function updateNetworkStatus(result) {
      const icon = document.getElementById('network-icon');
      const detail = document.getElementById('network-detail');
      const statusItem = document.getElementById('network-status');

      if (result.available) {
        icon.className = 'status-icon success';
        icon.textContent = '✓';
        detail.textContent = 'Internet connection available';
        statusItem.style.borderColor = '#27ae60';
      } else {
        icon.className = 'status-icon warning';
        icon.textContent = '!';
        detail.textContent = 'Limited connectivity - some features may not work';
        statusItem.style.borderColor = '#f39c12';
      }
    }

    function updateStorageStatus(result) {
      const icon = document.getElementById('storage-icon');
      const detail = document.getElementById('storage-detail');
      const statusItem = document.getElementById('storage-status');

      if (result.sufficient) {
        icon.className = 'status-icon success';
        icon.textContent = '✓';
        detail.textContent = `${result.available} available`;
        statusItem.style.borderColor = '#27ae60';
      } else {
        icon.className = 'status-icon warning';
        icon.textContent = '!';
        detail.textContent = `Only ${result.available} available - may need more space`;
        statusItem.style.borderColor = '#f39c12';
      }
    }

    function updateActions(container, network, storage) {
      const continueBtn = document.getElementById('continue-btn');

      if (container.available) {
        continueBtn.classList.remove('hidden');
        if (container.status === 'running') {
          continueBtn.className = 'btn success';
          continueBtn.innerHTML = '<span>🚀</span>Start ClaraVerse';
        } else {
          continueBtn.className = 'btn';
          continueBtn.innerHTML = `<span>🚀</span>Start ClaraVerse (${container.engine} will be started)`;
        }
      }
    }

    function retryCheck() {
      checkPrerequisites();
    }

    function continueSetup() {
      ipcRenderer.invoke('start-main-app');
    }

    function skipSetup() {
      ipcRenderer.invoke('start-main-app-limited');
    }

    function openGuide() {
      ipcRenderer.invoke('open-setup-guide');
    }

    function openExternal(url) {
      ipcRenderer.invoke('open-external', url);
    }

    // Start checking prerequisites when page loads
    window.addEventListener('DOMContentLoaded', () => {
      setTimeout(checkPrerequisites, 500);
    });
  </script>
</body>
</html>

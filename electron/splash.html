<!DOCTYPE html>
<html>
<head>
  <style>
    body {
      background: transparent;
      margin: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: #333;
      line-height: 1.5;
    }
    .container {
      background: rgba(255, 255, 255, 0.95);
      padding: 40px;
      border-radius: 24px;
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
      text-align: center;
      width: 800px;
      position: relative;
      overflow: hidden;
      margin: auto;
      -webkit-app-region: drag;
    }

    .container * {
      -webkit-app-region: no-drag;
    }
    .logo {
      width: 180px;
      height: 180px;
      margin-bottom: 25px;
    }
    h2 {
      font-size: 32px;
      font-weight: 500;
      margin: 15px 0;
      color: #222;
    }
    .message {
      font-size: 16px;
      color: #666;
      margin: 16px 0;
      padding: 0 20px;
    }
    .status {
      margin-top: 20px;
      font-size: 16px;
      color: #666;
      font-weight: 500;
    }
    .status.warning {
      color: #f39c12;
    }
    .status.error {
      color: #e74c3c;
    }
    .status.success {
      color: #2ecc71;
    }
    .progress-bar {
      width: 100%;
      height: 4px;
      background: #f0f0f0;
      border-radius: 2px;
      margin-top: 15px;
      overflow: hidden;
    }
    .progress-bar .fill {
      width: 0%;
      height: 100%;
      background: #3498db;
      transition: width 0.3s ease;
    }
    .spinner {
      margin-top: 15px;
      width: 28px;
      height: 28px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    .terminal {
      margin-top: 25px;
      background: #1e1e1e;
      border-radius: 12px;
      padding: 20px;
      height: 250px;
      width: calc(100% - 40px);
      overflow-y: auto;
      font-family: 'Courier New', monospace;
      font-size: 14px;
      color: #fff;
      text-align: left;
    }
    .terminal-line {
      margin: 2px 0;
      white-space: pre-wrap;
      word-break: break-all;
    }
    .terminal-line.info { color: #fff; }
    .terminal-line.success { color: #2ecc71; }
    .terminal-line.warning { color: #f39c12; }
    .terminal-line.error { color: #e74c3c; }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .fade {
      opacity: 1;
      transition: opacity 0.3s ease;
    }
    .fade.hide {
      opacity: 0;
    }
  </style>
</head>
<body>
  <div class="container fade">
    <img src="../../assets/icons/png/256x256.png" class="logo" alt="Clara" onerror="this.style.display='none'">
    <h2>Clara</h2>
    <div class="message">Setting up your environment. This may take a moment.</div>
    <div class="status" id="status">Initializing...</div>
    <div class="status error" id="error" style="display: none; margin-top: 8px; font-size: 13px;"></div>
    <div class="progress-bar">
      <div class="fill" id="progress"></div>
    </div>
    <div class="terminal" id="terminal"></div>
    <div class="spinner"></div>
  </div>
  <script>
    const { ipcRenderer } = require('electron');

    function addTerminalLine(message, type = 'info') {
      const terminal = document.getElementById('terminal');
      const line = document.createElement('div');
      line.className = `terminal-line ${type}`;
      line.textContent = message;
      terminal.appendChild(line);
      terminal.scrollTop = terminal.scrollHeight;
    }

    function updateStatus(message, type = 'info') {
      console.log(`[Splash] ${type}: ${message}`);
      const statusEl = document.getElementById('status');
      const errorEl = document.getElementById('error');
      const progressEl = document.getElementById('progress');

      statusEl.textContent = message;
      statusEl.className = 'status ' + type;

      // Add message to terminal
      addTerminalLine(message, type);

      if (type === 'error') {
        errorEl.textContent = message;
        errorEl.style.display = 'block';
      }

      // Update progress bar for certain messages
      if (message.includes('Downloading')) {
        const match = message.match(/(\d+)%/);
        if (match) {
          progressEl.style.width = `${match[1]}%`;
        }
      }
    }

    function hide() {
      document.querySelector('.container').classList.add('hide');
    }

    function show() {
      document.querySelector('.container').classList.remove('hide');
    }

    ipcRenderer.on('status', (_, data) => {
      if (typeof data === 'string') {
        updateStatus(data);
      } else {
        updateStatus(data.message, data.type);
      }
    });

    ipcRenderer.on('hide', hide);
    ipcRenderer.on('show', show);

    // Log any uncaught errors
    window.onerror = function(msg, url, line) {
      updateStatus(`Error: ${msg} (${url}:${line})`, 'error');
    };
  </script>
</body>
</html>

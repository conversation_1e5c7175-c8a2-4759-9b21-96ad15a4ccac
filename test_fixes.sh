#!/bin/bash

# Test script for Clara<PERSON><PERSON> fixes
echo "Running ClaraVerse Fix Verification Tests"
echo "========================================="

# Create log directory
LOG_DIR="fix_test_logs"
mkdir -p $LOG_DIR
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$LOG_DIR/test_results_$TIMESTAMP.log"

echo "Test started at $(date)" | tee -a $LOG_FILE
echo "System: $(uname -a)" | tee -a $LOG_FILE
echo "" | tee -a $LOG_FILE

# Function to log with timestamp
log() {
  echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1" | tee -a $LOG_FILE
}

# Function to run tests with status indicator
run_test() {
  local test_name=$1
  local test_cmd=$2
  
  echo "" | tee -a $LOG_FILE
  log "==== TESTING: $test_name ===="
  
  if eval "$test_cmd" >> $LOG_FILE 2>&1; then
    log "✅ PASSED: $test_name"
    return 0
  else
    log "❌ FAILED: $test_name"
    return 1
  fi
}

# Test Docker network creation
test_docker_network() {
  log "Testing Docker network creation"
  
  # Check if Docker is running
  if ! docker info &>/dev/null; then
    log "Docker is not running. Please start Docker and run this test again."
    return 1
  fi
  
  # Remove existing network if it exists (to test creation)
  docker network rm clara_network &>/dev/null
  
  # Start the app and let it create the network
  log "Starting app to create network..."
  npm run start &
  APP_PID=$!
  sleep 10
  kill $APP_PID
  
  # Check if network was created
  if docker network inspect clara_network &>/dev/null; then
    log "Network clara_network created successfully."
    
    # Test double creation (should handle gracefully)
    log "Testing network idempotence (creating again)..."
    npm run start &
    APP_PID=$!
    sleep 10
    kill $APP_PID
    
    if docker network inspect clara_network &>/dev/null; then
      log "Network handling works correctly when network already exists"
      return 0
    else
      log "Network disappeared after second start - this is unexpected"
      return 1
    fi
  else
    log "Failed to create clara_network"
    return 1
  fi
}

# Test theme persistence
test_theme_persistence() {
  log "Testing theme persistence"
  
  # Clean any existing theme setting
  rm -f .claraverse_theme
  
  # Start app with light theme (default)
  log "Starting app with default theme..."
  npm run start &
  APP_PID=$!
  sleep 5
  
  # TODO: Programmatically click dark mode toggle
  # This will require UI automation, for now just log the test
  log "NOTE: Manual verification required for dark mode toggle"
  log "1. Click the dark mode toggle in the UI"
  log "2. Restart the app"
  log "3. Verify the dark mode persists"
  
  kill $APP_PID
  return 0
}

# Run all tests
main() {
  log "Starting test suite"
  
  # Test 1: Docker Network
  run_test "Docker Network Creation" test_docker_network
  DOCKER_TEST_RESULT=$?
  
  # Test 2: Theme Persistence 
  run_test "Theme Persistence" test_theme_persistence
  THEME_TEST_RESULT=$?
  
  # Summary
  echo "" | tee -a $LOG_FILE
  log "==== TEST SUMMARY ===="
  
  if [ $DOCKER_TEST_RESULT -eq 0 ]; then
    log "Docker Network Test: ✅ PASSED"
  else
    log "Docker Network Test: ❌ FAILED"
  fi
  
  if [ $THEME_TEST_RESULT -eq 0 ]; then
    log "Theme Persistence Test: ✅ PASSED (manual verification required)"
  else
    log "Theme Persistence Test: ❌ FAILED"
  fi
  
  # Overall result
  if [ $DOCKER_TEST_RESULT -eq 0 ] && [ $THEME_TEST_RESULT -eq 0 ]; then
    log "ALL TESTS COMPLETED SUCCESSFULLY"
    return 0
  else
    log "SOME TESTS FAILED. CHECK LOG FOR DETAILS."
    return 1
  fi
}

# Run the main function
main
echo "Test log saved to $LOG_FILE" 